class PlayerTracker extends GSController {
    known_players = null;

    function Start()
    {
        this.known_players = {};

        // Na<PERSON><PERSON> a<PERSON>u<PERSON> h<PERSON>
        foreach (client_id in GSClientList()) {
            this.known_players[client_id] <- true;
        }

        // Zaregistruj se pro události připojení/odpojen<PERSON> klientů
        GSEventController.Subscribe(GSEvent.ET_CLIENT_INFO);
        GSEventController.Subscribe(GSEvent.ET_CLIENT_DISCONNECT);

        // Zpráva do chatu po spuštění skriptu
        GSNews.Create(GSNews.NT_GENERAL, "🟢 Player Tracker byl úspěšně spuštěn.", GSCompany.COMPANY_INVALID);
    }

    function HandleEvent(event)
    {
        switch (event.GetEventType()) {
            case GSEvent.ET_CLIENT_INFO: {
                local client_id = event.GetClientID();
                if (!(client_id in this.known_players)) {
                    this.OnNewPlayer(client_id);
                    this.known_players[client_id] <- true;
                }
                break;
            }

            case GSEvent.ET_CLIENT_DISCONNECT: {
                local client_id = event.GetClientID();
                if (client_id in this.known_players) {
                    delete this.known_players[client_id];
                }
                break;
            }
        }
    }

    function OnNewPlayer(client_id)
    {
        try {
            local name = GSClient.GetName(client_id);
            local ip = GSClient.GetIP(client_id);
            local company_id = GSClient.GetCompany(client_id);

            local status = "[Divák]";
            if (company_id >= 0 && company_id < GSCompany.COMPANY_FIRST) {
                status = GSCompany.GetName(company_id);
            }

            local message = "✅ Připojen hráč: " +
                            "ID: " + client_id + ", " +
                            "Jméno: " + name + ", " +
                            "IP: " + ip + ", " +
                            "Společnost: " + status;

            GSNews.Create(GSNews.NT_GENERAL, message, GSCompany.COMPANY_INVALID);
        } catch (e) {
            GSLog.Error("Chyba při zpracování hráče: " + e);
        }
    }
}
